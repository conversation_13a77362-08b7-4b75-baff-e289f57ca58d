name: exptrans
channels:
  - defaults
dependencies:
  - ca-certificates=2022.4.26=haa95532_0
  - certifi=2022.5.18.1=py37haa95532_0
  - openssl=1.1.1o=h2bbff1b_0
  - pip=21.2.4=py37haa95532_0
  - python=3.7.13=h6244533_0
  - setuptools=61.2.0=py37haa95532_0
  - sqlite=3.38.3=h2bbff1b_0
  - vc=14.2=h21ff451_1
  - vs2015_runtime=14.27.29016=h5e58377_2
  - wheel=0.37.1=pyhd3eb1b0_0
  - wincertstore=0.2=py37haa95532_2
  - pip:
    - appdirs==1.4.4
    - audioread==2.1.9
    - cffi==1.15.0
    - charset-normalizer==2.0.12
    - click==8.1.3
    - colorama==0.4.4
    - cycler==0.11.0
    - decorator==5.1.1
    - filelock==3.7.1
    - flatbuffers==2.0
    - fonttools==4.36.0
    - freetype-py==2.3.0
    - huggingface-hub==0.0.8
    - idna==3.3
    - imageio==2.19.3
    - importlib-metadata==4.11.4
    - joblib==1.1.0
    - kiwisolver==1.4.4
    - kornia==0.6.6
    - librosa==0.8.1
    - llvmlite==0.38.1
    - matplotlib==3.5.3
    - networkx==2.6.3
    - numba==0.55.2
    - numpy==1.21.6
    - nvdiffrast==0.2.5
    - onnxruntime-gpu==1.9.0
    - opencv-python==********
    - packaging==21.3
    - pillow==9.1.1
    - pooch==1.6.0
    - protobuf==4.21.5
    - psutil==5.9.1
    - pycparser==2.21
    - pyglet==1.5.26
    - pyopengl==3.1.0
    - pyparsing==3.0.9
    - pyrender==0.1.45
    - python-dateutil==2.8.2
    - pywavelets==1.3.0
    - pyyaml==6.0
    - regex==2022.6.2
    - requests==2.27.1
    - resampy==0.2.2
    - sacremoses==0.0.53
    - scikit-image==0.19.3
    - scikit-learn==1.0.2
    - scipy==1.7.1
    - six==1.16.0
    - soundfile==0.10.3.post1
    - threadpoolctl==3.1.0
    - tifffile==2021.11.2
    - tokenizers==0.10.3
    - torch==1.11.0+cu113
    - torchaudio==0.11.0+cu113
    - torchvision==0.12.0+cu113
    - tqdm==4.64.0
    - transformers==4.6.1
    - trimesh==3.12.7
    - typeguard==2.13.3
    - typing-extensions==4.2.0
    - urllib3==1.26.9
    - zipp==3.8.0
prefix: C:\ProgramData\Anaconda3\envs\exptrans
