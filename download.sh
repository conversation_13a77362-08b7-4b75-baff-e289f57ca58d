set -e
set -u

# face attr
mkdir -p face_attr_detect
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/face_attr_epoch_12_220318.onnx -O face_attr_detect/face_attr_epoch_12_220318.onnx

# face detect
mkdir -p face_detect_utils/resources
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/pfpld_robust_sim_bs1_8003.onnx -O face_detect_utils/resources/pfpld_robust_sim_bs1_8003.onnx
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/scrfd_500m_bnkps_shape640x640.onnx -O face_detect_utils/resources/scrfd_500m_bnkps_shape640x640.onnx
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/model_float32.onnx -O face_detect_utils/resources/model_float32.onnx

# dh model
mkdir -p landmark2face_wy/checkpoints/anylang
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/dinet_v1_20240131.pth -O landmark2face_wy/checkpoints/anylang/dinet_v1_20240131.pth

# face parsing
mkdir -p pretrain_models/face_lib/face_parsing
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/79999_iter.onnx -O pretrain_models/face_lib/face_parsing/79999_iter.onnx

# gfpgan
mkdir -p pretrain_models/face_lib/face_restore/gfpgan
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/GFPGANv1.4.onnx -O pretrain_models/face_lib/face_restore/gfpgan/GFPGANv1.4.onnx

# xseg
mkdir -p xseg
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/xseg_211104_4790000.onnx -O xseg/xseg_211104_4790000.onnx

# wenet
mkdir -p wenet/examples/aishell/aidata/exp/conformer
wget https://dc-purchase.obs.cn-east-3.myhuaweicloud.com/models/wenetmodel.pt -O wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt