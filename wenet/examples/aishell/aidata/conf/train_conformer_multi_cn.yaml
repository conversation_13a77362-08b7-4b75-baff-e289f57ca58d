# network architecture
# encoder related
encoder: conformer
encoder_conf:
    output_size: 256    # dimension of attention
    attention_heads: 4
    linear_units: 2048  # the number of units of position-wise feed forward
    num_blocks: 12      # the number of encoder blocks
    dropout_rate: 0.1
    positional_dropout_rate: 0.1
    attention_dropout_rate: 0.0
    input_layer: conv2d # encoder input type, you can chose conv2d, conv2d6 and conv2d8
    normalize_before: true
    cnn_module_kernel: 15
    use_cnn_module: True
    activation_type: 'swish'
    pos_enc_layer_type: 'rel_pos'
    selfattention_layer_type: 'rel_selfattn'

# decoder related
decoder: transformer
decoder_conf:
    attention_heads: 4
    linear_units: 2048
    num_blocks: 6
    dropout_rate: 0.1
    positional_dropout_rate: 0.1
    self_attention_dropout_rate: 0.0
    src_attention_dropout_rate: 0.0

# hybrid CTC/attention
model_conf:
    ctc_weight: 0.3
    lsm_weight: 0.1     # label smoothing option
    length_normalized_loss: false

# use raw_wav or kaldi feature
raw_wav: false

# feature extraction
collate_conf:
    # waveform level config
    wav_distortion_conf:
        wav_dither: 0.1
        wav_distortion_rate: 0.0
        distortion_methods: []
    speed_perturb: true
    feature_extraction_conf:
        feature_type: 'fbank'
        mel_bins: 80
        frame_shift: 10
        frame_length: 25
        using_pitch: false
    # spec level config
    # spec_swap: false
    feature_dither: 0.0 # add dither [-feature_dither,feature_dither] on fbank feature
    spec_aug: true
    spec_aug_conf:
        warp_for_time: False
        num_t_mask: 2
        num_f_mask: 2
        max_t: 50
        max_f: 10
        max_w: 80


# dataset related
dataset_conf:
    max_length: 1300     #40960
    min_length: 0
    batch_type: 'static' # static or dynamic
    batch_size: 40
    sort: true

grad_clip: 5
accum_grad: 4
max_epoch: 240
log_interval: 100

optim: adam
optim_conf:
    lr: 0.0025  #0.0025
scheduler: warmuplr     # pytorch v1.1.0+ required
scheduler_conf:
    warmup_steps: 100000
