import re
from PIL import Image
from scipy import signal
import time, numpy as np, cv2
video_time = []
import multiprocessing.dummy as mp
import multiprocessing as real_mp
from concurrent.futures import ThreadPoolExecutor, as_completed
import functools
from numba import jit, prange
import warnings
warnings.filterwarnings('ignore')

# JIT编译的数值计算函数
@jit(nopython=True, cache=True)
def fast_bbox_expansion(x1, y1, x2, y2, w, h, expand_ratio=0.1):
    """快速边界框扩展计算"""
    expansion_x = (x2 - x1) * expand_ratio
    expansion_y = (y2 - y1) * expand_ratio
    
    new_x1 = max(0, int(x1 - expansion_x))
    new_y1 = max(0, y1)
    new_x2 = min(w, int(x2 + expansion_x))
    new_y2 = min(h, int(y2 + expansion_y))
    
    return new_x1, new_y1, new_x2, new_y2

@jit(nopython=True, cache=True)
def fast_3dmm_bounds(xmin, ymin, w_rect, h_rect, img_w, img_h):
    """快速3DMM边界计算"""
    wh_ratio = w_rect / h_rect
    x_c = xmin + w_rect * 0.5
    half_width = w_rect / wh_ratio * 0.8
    height_factor = w_rect / wh_ratio
    
    Xmin_3dmm = max(0, int(x_c - half_width))
    Xmax_3dmm = min(img_w, int(x_c + half_width))
    Ymin_3dmm = max(0, int(ymin - height_factor * 0.35))
    Ymax_3dmm = min(img_h, int(ymin + height_factor * 1.25))
    
    return Xmin_3dmm, Xmax_3dmm, Ymin_3dmm, Ymax_3dmm

@jit(nopython=True, cache=True)
def fast_crop_bounds(xmin, ymin, w, img_w, img_h, wh_ratio_factor):
    """快速裁剪边界计算"""
    x_c = xmin + w * 0.5
    half_width = w * wh_ratio_factor * 0.75
    height_factor = w * wh_ratio_factor
    
    Xmin = max(0, int(x_c - half_width))
    Xmax = min(img_w, int(x_c + half_width))
    Ymin = max(0, int(ymin - height_factor * 0.15))
    Ymax = min(img_h, int(ymin + height_factor * 1.35))
    
    return Xmin, Xmax, Ymin, Ymax

@jit(nopython=True, cache=True)
def fast_landmark_transform(landmarks, xmin, ymin, scale_x, scale_y):
    """快速地标点变换"""
    result = np.zeros_like(landmarks)
    for i in prange(landmarks.shape[0]):
        result[i, 0] = (landmarks[i, 0] - xmin) * scale_x
        result[i, 1] = (landmarks[i, 1] - ymin) * scale_y
    return result

@jit(nopython=True, cache=True)
def fast_pose_check(head_poses, pose_threshold):
    """快速姿态检查"""
    for i in prange(3):
        if not (pose_threshold[i, 0] < head_poses[i] < pose_threshold[i, 1]):
            return False
    return True

class op:
    def __init__(self, caped_img2, wh, scrfd_detector, scrfd_predictor, hp, lm3d_std, img_size, driver_flag):
        # 保持向后兼容性，同时使用优化的数据结构
        self.data_dict = dict(caped_img2)
        
        # 为了向后兼容，创建一个mp_dict属性
        self.manager = mp.Manager
        self.mp_dict = self.manager().dict()
        self.mp_dict.update(caped_img2)
        
        self.img_size = img_size
        self.target_size = self.img_size + int(self.img_size / 256) * 10
        
        self.wh = wh
        self.scrfd_detector = scrfd_detector
        self.scrfd_predictor = scrfd_predictor
        self.hp = hp
        self.pose_threshold = np.array([[-70, 50], [-100, 100], [-70, 70]], dtype=np.float64)
        self.driver_flag = driver_flag
        self.no_face = []
        
        # 预计算常用值
        self.wh_ratio_factor = 1.0 / self.wh
        self.target_size_float = float(self.target_size)
        
        # 预分配数组以减少内存分配
        self.temp_landmarks = np.zeros((68, 2), dtype=np.float32)  # 假设最多68个地标点
        
        # 批处理大小
        self.batch_size = min(8, max(1, len(caped_img2) // 4))

    def get_max_face_vectorized(self, face_boxes):
        """向量化的最大面部检测"""
        if face_boxes.shape[0] == 1:
            return face_boxes[0].astype(np.int32)
        
        # 使用更快的面积计算
        areas = np.multiply(
            face_boxes[:, 2] - face_boxes[:, 0], 
            face_boxes[:, 3] - face_boxes[:, 1]
        )
        return face_boxes[np.argmax(areas)].astype(np.int32)

    def process_face_detection_batch(self, idx_batch):
        """批处理面部检测"""
        results = {}
        
        for idx in idx_batch:
            try:
                loc_dict = self.data_dict[idx].copy()
                img = loc_dict['imgs_data']
                h, w = img.shape[:2]
                
                face_boxes, _ = self.scrfd_detector.get_bboxes(img)
                
                if face_boxes.shape[0] > 0:
                    x1, y1, x2, y2, score = self.get_max_face_vectorized(face_boxes)
                    
                    # 使用JIT编译的函数
                    x1, y1, x2, y2 = fast_bbox_expansion(x1, y1, x2, y2, w, h)
                    
                    face_img = img[y1:y2, x1:x2]
                    pots = self.scrfd_predictor.forward(face_img)[0]
                    landmarks = pots.astype(np.int32) + np.array([x1, y1])
                    
                    xmin, ymin, w_rect, h_rect = cv2.boundingRect(landmarks)
                    
                    # 使用JIT编译的3DMM边界计算
                    Xmin_3dmm, Xmax_3dmm, Ymin_3dmm, Ymax_3dmm = fast_3dmm_bounds(
                        xmin, ymin, w_rect, h_rect, w, h
                    )
                    
                    head_poses = self.hp.get_head_pose(img[Ymin_3dmm:Ymax_3dmm, Xmin_3dmm:Xmax_3dmm])
                    
                    # 使用JIT编译的姿态检查
                    if fast_pose_check(head_poses, self.pose_threshold):
                        loc_dict['bounding_box_p'] = np.array([y1, y2, x1, x2], dtype=np.int32)
                    else:
                        loc_dict['bounding_box_p'] = np.array([], dtype=np.int32)
                else:
                    loc_dict['bounding_box_p'] = np.array([], dtype=np.int32)
                
                results[idx] = loc_dict
                
            except Exception as e:
                print(f"Error processing face detection for idx {idx}: {e}")
                results[idx] = self.data_dict[idx]
                
        return results

    def process_face_crop_batch(self, idx_batch):
        """批处理面部裁剪"""
        results = {}
        
        for idx in idx_batch:
            try:
                loc_dict = self.data_dict[idx].copy()
                img = loc_dict["imgs_data"]
                dets = loc_dict.get("bounding_box_p", np.array([]))
                
                if len(dets) == 0 or (len(dets) > 0 and np.max(dets) == 0.0):
                    self.no_face.append(idx)
                    dets = np.array([0, 100, 0, 100], dtype=np.int32)
                    loc_dict["bounding_box_p"] = np.zeros(4, dtype=np.int32)
                
                if len(dets) >= 4:
                    y1, y2, x1, x2 = dets.astype(np.int32)
                    
                    face_region = img[y1:y2, x1:x2]
                    if face_region.size > 0:
                        face_landmarks = self.scrfd_predictor.forward(face_region)[0]
                        landmarks = face_landmarks + np.array([x1, y1])
                        loc_dict["landmarks"] = landmarks
                        
                        xmin, ymin, w, h = cv2.boundingRect(landmarks.astype(np.int32))
                        
                        # 使用JIT编译的裁剪边界计算
                        Xmin, Xmax, Ymin, Ymax = fast_crop_bounds(
                            xmin, ymin, w, img.shape[1], img.shape[0], self.wh_ratio_factor
                        )
                        
                        loc_dict["bounding_box"] = np.array([Ymin, Ymax, Xmin, Xmax], dtype=np.int32)
                        
                        # 使用JIT编译的地标点变换
                        scale_x = self.target_size_float / (Xmax - Xmin)
                        scale_y = self.target_size_float / (Ymax - Ymin)
                        lm_crop = fast_landmark_transform(landmarks, Xmin, Ymin, scale_x, scale_y)
                        
                        img_crop = img[Ymin:Ymax, Xmin:Xmax]
                        
                        if img_crop.size > 0:
                            if self.driver_flag:
                                img_crop = cv2.cvtColor(
                                    cv2.resize(img_crop, (self.target_size, self.target_size), 
                                              interpolation=cv2.INTER_CUBIC), 
                                    cv2.COLOR_BGR2RGB
                                )
                            else:
                                img_crop = cv2.resize(img_crop, (self.target_size, self.target_size), 
                                                    interpolation=cv2.INTER_CUBIC)
                            
                            loc_dict["crop_lm"] = lm_crop
                            loc_dict["crop_img"] = img_crop
                
                results[idx] = loc_dict
                
            except Exception as e:
                print(f"Error processing face crop for idx {idx}: {e}")
                results[idx] = self.data_dict[idx]
                
        return results

    def smooth_optimized(self):
        """优化的平滑处理"""
        keylist = np.array(sorted(self.data_dict.keys()))
        max_len = len(keylist)
        
        # 预分配数组
        bbx_smooth = np.zeros((max_len, 4), dtype=np.float32)
        
        # 向量化数据提取
        for i, key in enumerate(keylist):
            bbox = self.data_dict[key].get("bounding_box_p", np.array([]))
            if len(bbox) == 4:
                bbx_smooth[i, :] = bbox
            elif i > 0:
                bbx_smooth[i, :] = bbx_smooth[i-1, :]
        
        # 使用更高效的卷积
        conv_core = np.array([[0.2], [0.2], [0.2], [0.2], [0.2]], dtype=np.float32)
        try:
            bbx_smooth2 = signal.convolve2d(bbx_smooth, conv_core, boundary="symm", mode="same")
            
            # 向量化差异检测
            diff_values = np.sum(np.abs(bbx_smooth2 - bbx_smooth), axis=1)
            diff_mask = diff_values > 12
            
            bbx_smooth3 = bbx_smooth2.copy()
            bbx_smooth3[diff_mask] = bbx_smooth[diff_mask]
            
            bbx_smooth4 = signal.convolve2d(bbx_smooth3, conv_core, boundary="symm", mode="same")
            
            # 批量更新结果
            updates = {}
            for i, key in enumerate(keylist):
                loc_dict = self.data_dict[key].copy()
                loc_dict["bounding_box_p"] = bbx_smooth4[i, :].astype(np.int32)
                updates[key] = loc_dict
            
            self.data_dict.update(updates)
            
        except Exception as e:
            print(f"Smoothing failed: {e}, skipping smoothing step")

    def sync_data_to_mp_dict(self):
        """将优化处理的结果同步回mp_dict以保持兼容性"""
        self.mp_dict.clear()
        self.mp_dict.update(self.data_dict)

    def flow_optimized(self):
        """高度优化的处理流程"""
        keys = list(self.data_dict.keys())
        
        # 创建批次
        key_batches = [keys[i:i + self.batch_size] for i in range(0, len(keys), self.batch_size)]
        
        # 并行处理面部检测
        max_workers = min(len(key_batches), real_mp.cpu_count())
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_batch = {
                executor.submit(self.process_face_detection_batch, batch): batch 
                for batch in key_batches
            }
            
            # 收集结果
            for future in as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    self.data_dict.update(batch_results)
                except Exception as e:
                    print(f"Face detection batch failed: {e}")
        
        # 并行处理面部裁剪
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_batch = {
                executor.submit(self.process_face_crop_batch, batch): batch 
                for batch in key_batches
            }
            
            for future in as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    self.data_dict.update(batch_results)
                except Exception as e:
                    print(f"Face crop batch failed: {e}")
        
        # 同步结果回mp_dict以保持兼容性
        self.sync_data_to_mp_dict()

    # 向后兼容的方法
    def show(self):
        for idx in self.mp_dict.keys():
            print(self.mp_dict[idx], idx)
    
    def smooth_(self):
        self.smooth_optimized()
        # 同步结果回mp_dict
        self.sync_data_to_mp_dict()
    
    def flow(self):
        self.flow_optimized()
        
    # 获取结果的方法
    def get_results(self):
        """获取处理结果"""
        return dict(self.data_dict)