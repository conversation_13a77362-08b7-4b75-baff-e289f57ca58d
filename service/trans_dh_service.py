
import collections
import pickle
import random
import gc, multiprocessing, os, subprocess, threading, time, traceback
from enum import Enum
from multiprocessing import Process, set_start_method
from queue import Empty, Full
from typing import Optional

import cv2, lib<PERSON>a, numpy as np, torch
from cv2box import C<PERSON><PERSON>
from cv2box.cv_gears import <PERSON><PERSON>, Queue, CVVideoWriterThread
from face_detect_utils.face_detect import FaceDetect, pfpld
from face_detect_utils.head_pose import Headpose
from face_lib.face_detect_and_align import FaceDetect5Landmarks
from face_lib.face_restore import GFPGAN
from h_utils.custom import CustomError
from h_utils.request_utils import download_file
from h_utils.sweep_bot import sweep
from landmark2face_wy.digitalhuman_interface import DigitalHumanModel
from preprocess_audio_and_3dmm import op
from wenet.compute_ctc_att_bnf import get_weget,compute_bnf_data
from wenet.compute_ctc_att_bnf import load_ppg_model
from y_utils.config import GlobalConfig
from y_utils.logger import logger as logger

# 用于从音频文件中提取特征
def feature_extraction_wenet(audio_file, fps, wenet_model, mfccnorm=True, section=560000):
    wenet_feature_list = []
    rate = 16000
    win_size = 20
    sig = audio_file
    time_duration = len(sig) / rate
    cnts = range(int(time_duration * fps))
    indexs = []
    f_wenet_all = compute_bnf_data(sig, wenet_model, section)
    for cnt in cnts:
        c_count = int(cnt / cnts[-1] * (f_wenet_all.shape[0] - 20)) + win_size // 2
        indexs.append(f_wenet_all[c_count - win_size // 2:c_count + win_size // 2, ...])
    for i in range(len(indexs)):
        wenet_feature_list.append([indexs[i]])
    return wenet_feature_list


# 将预测的掩码（mask）融合到原始图像的指定区域，生成完整的输出图像列表
def get_complete_imgs(output_img_list, start_index, params):
    (out_shape, output_resize, drivered_imgs_data, Y1_list, Y2_list, X1_list, X2_list) = params
    complete_imgs = []
    for (i, mask_B_pre) in enumerate(output_img_list):
        img_idx = start_index + i
        image = drivered_imgs_data[img_idx]
        (y1, y2, x1, x2) = (Y1_list[img_idx], Y2_list[img_idx], X1_list[img_idx], X2_list[img_idx])
        mask_B_pre_resize = cv2.resize(mask_B_pre, (y2 - y1, x2 - x1))
        if y1 < 0:
            mask_B_pre_resize = mask_B_pre_resize[:, -y1:]
            y1 = 0
        if y2 > image.shape[1]:
            mask_B_pre_resize = mask_B_pre_resize[:, :-(y2 - image.shape[1])]
            y2 = image.shape[1]
        if x1 < 0:
            mask_B_pre_resize = mask_B_pre_resize[-x1:, :]
            x1 = 0
        if x2 > image.shape[0]:
            mask_B_pre_resize = mask_B_pre_resize[:-(x2 - image.shape[0]), :]
            x2 = image.shape[0]
        image[x1:x2, y1:y2] = mask_B_pre_resize
        image = cv2.resize(image, (out_shape[1] // output_resize, out_shape[0] // output_resize))
        complete_imgs.append(image)
    return complete_imgs

# 将音频数据与面部特征结合，通过数字人模型生成动态面部图像，并将结果整合到完整图像中。
def get_blend_imgs(batch_size, audio_data, face_data_dict, blend_dynamic, params, digital_human_model, frameId):
    """批量处理音频数据生成混合图像"""
    result_img_list = []
    total_batches = (len(audio_data) + batch_size - 1) // batch_size  # 使用向上取整
    for idx in range(total_batches):
        torch.cuda.empty_cache()
        start_index = idx * batch_size
        current_batch_size = min(batch_size, len(audio_data) - start_index)
        output_img_list = digital_human_model.inference_notraining(
            audio_data, face_data_dict, current_batch_size, start_index, 
            blend_dynamic, params, frameId
        )
        complete_imgs = get_complete_imgs(output_img_list, start_index, params)
        result_img_list.extend(complete_imgs)
    return result_img_list



def drivered_video_pn_suan(drivered_path,frame_list):
    try:
        code = 'test'
        count_f = 0
        cap = None
        print("drivered_video >>>>>>>>>>>>>>>>>>>> 开始第一次循环")
        cap = cv2.VideoCapture(drivered_path)
        if not cap.isOpened():
            print(f"无法打开视频文件: {drivered_path}")
            raise Exception(f"无法打开视频文件: {drivered_path}")
        while True:
            (ret, frame) = cap.read()
            if not ret:
                break
            count_f += 1
            # cv2.imwrite(os.path.join(temp_path, f"{count_f}.png"), frame)
            frame_list.append([frame])
        cap.release()
        return frame_list
    except Full:
        print("[{}]任务视频驱动队列满，严重阻塞，下游队列异常".format(code))
    except Exception as e:
        print("[{}]任务视频驱动队列异常，异常信息:[{}]".format(code, str(e)))
        print(traceback.format_exc())
        try:
            pass
        except Exception as queue_ex:
            print(f"向队列发送异常信息失败: {str(queue_ex)}")
    finally:
        # 确保资源被正确释放
        if cap and cap.isOpened():
            cap.release()



# 生成一个椭圆形的面部掩码（mask），用于图像合成或面部处理任务。该掩码中心区域为 1（白色），边缘区域逐渐过渡到 0（黑色），并可调整大小以适应不同的输入需求
def get_face_mask(mask_shape=(512, 512)):
    mask = np.zeros((512, 512)).astype(np.float32)
    cv2.ellipse(mask, (256, 256), (220, 160), 90, 0, 360, (255, 255, 255), -1)
    thres = 20
    mask[:thres, :] = 0
    mask[-thres:, :] = 0
    mask[:, :thres] = 0
    mask[:, -thres:] = 0
    mask = cv2.stackBlur(mask, (201, 201))
    mask = mask / 255.0
    mask = cv2.resize(mask, mask_shape)
    return mask[(..., np.newaxis)]



face_mask = get_face_mask()
need_chaofen_flag = False
get_firstface_frame = False


def audio_transfer_suan(digital_human_model, frame_list, audio_list, op_list, w, h, batch_size=1):
    output_resize = 1
    ts = []
    start_time = time.time()
    
    for i in range(len(audio_list)):
        try:
            img_list, audio_feature_list, frameId = frame_list[i], audio_list[i], i
            out_shape = img_list[0].shape
            drivered_face_dict = op_list[i]
            x1_list, x2_list, y1_list, y2_list = [], [], [], []
            for idx in range(len(drivered_face_dict)):
                facebox = drivered_face_dict[idx]['bounding_box']
                x1_list.append(facebox[0])
                x2_list.append(facebox[1])
                y1_list.append(facebox[2])
                y2_list.append(facebox[3])
                
            drivered_exceptlist = []
            keylist = list(drivered_face_dict.keys())
            keylist.sort()
            valid_index = -1
            for j in keylist:
                if len(drivered_face_dict[j]['bounding_box_p']) == 4:
                    valid_index = j
                    break
            for j in keylist:
                if len(drivered_face_dict[j]['bounding_box_p']) != 4:
                    drivered_exceptlist.append(j)
                    print(drivered_exceptlist, '-------------------------------------')
            if valid_index != -1:
                for j in drivered_exceptlist:
                    drivered_face_dict[j]['bounding_box_p'] = drivered_face_dict[valid_index]['bounding_box_p']
                    drivered_face_dict[j]['bounding_box'] = drivered_face_dict[valid_index]['bounding_box']
                    drivered_face_dict[j]['crop_lm'] = drivered_face_dict[valid_index]['crop_lm']
                    drivered_face_dict[j]['crop_img'] = drivered_face_dict[valid_index]['crop_img']
            else:
                for it in keylist:
                    if len(drivered_face_dict[it]['bounding_box_p']) != 4:
                        print(it, '++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
                        if it > 0:
                            drivered_face_dict[it]['bounding_box_p'] = drivered_face_dict[it - 1]['bounding_box_p']
                            drivered_face_dict[it]['bounding_box'] = drivered_face_dict[it - 1]['bounding_box']
                            drivered_face_dict[it]['crop_lm'] = drivered_face_dict[it - 1]['crop_lm']
                            drivered_face_dict[it]['crop_img'] = drivered_face_dict[it - 1]['crop_img']
                        else:
                            logger.warning(f"第一帧无效: {it}")
            params = [out_shape, output_resize, img_list, y1_list, y2_list, x1_list, x2_list]
            output_imgs = get_blend_imgs(
                batch_size, audio_feature_list, drivered_face_dict,
                GlobalConfig.instance().blend_dynamic, params, digital_human_model, frameId
            )
            
            ts.append(output_imgs)
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(traceback.format_exc())
            print("数字人处理失败")
            time.sleep(1)
            torch.cuda.empty_cache()
    
    end_time = time.time()
    total_time = end_time - start_time
    logger.error('数字人进程结束')
    print(f'数字人处理总耗时: {total_time:.4f}s')
    return ts



def write_video_suan(value_list, width, height, fps,audio_path):
    xx = str(random.randint(0, 1000000))
    output_mp4 = os.path.join('temp', "{}-t.mp4".format(f'test{xx}'))
    result_path = os.path.join('temp', "{}-r.mp4".format(f'test1{xx}'))
    fourcc = (cv2.VideoWriter_fourcc)(*"mp4v")
    video_write = cv2.VideoWriter(output_mp4, fourcc, fps, (
     width, height))
    for value_ in value_list:
            for result_img in value_:
                video_write.write(result_img)
    video_write.release()
    print(output_mp4)
    command = "ffmpeg -loglevel warning -y -i {} -i {} -c:a aac -c:v libx264 -crf 15 -strict -2 {}".format(audio_path, output_mp4, result_path)
    subprocess.call(command, shell=True)
    return result_path


class Status(Enum):
    run = 1
    success = 2
    error = 3




def init_wh_process_suan(driver_path,out_queue_suan):  # [cite: 1920, 1921, 1928]
    face_detector = FaceDetect(cpu=False, model_path='face_detect_utils/resources/')  # [cite: 1950, 1951, 1952]
    plfd = pfpld(cpu=False, model_path='face_detect_utils/resources/')  # [cite: 1952, 1953, 1954]
    logger.info(">>> init_wh_process进程启动")  # [cite: 1955, 1956]
    try:  # [cite: 1957]
        code = 'test'  # [cite: 1957, 1958, 1959, 1960]
        s = time.time()  # [cite: 1960, 1961, 1962]
        wh_list = []  # [cite: 1962]
        cap = cv2.VideoCapture(driver_path)  # [cite: 1963, 1964, 1965]
        count = 0  # [cite: 1965, 1966]
        has_multi_face = False  # [cite: 1966, 1967]
        try:  # [cite: 1967]
            while True:  # [cite: 1968, 1969, 1970, 1971]
                ret, frame = cap.read()  # [cite: 1971, 1972, 1973]
                if not ret: break  # [cite: 1974]
                bboxes = []  # [cite: 1975]
                kpss = []  # Placeholder, kpss is UNPACK_SEQUENCE target [cite: 1979]
                try:  # [cite: 1976]
                    bboxes, kpss = face_detector.get_bboxes(frame)  # [cite: 1976, 1977, 1978, 1979]
                except Exception as e_bbox:  # [cite: 1980, 1981]
                    logger.error(f"[{code}]init_wh exception: {e_bbox}")  # [cite: 1984, 1985, 1986]
                bboxes_len = len(bboxes)  # [cite: 1990, 1991, 1992]
                if bboxes_len > 0:  # [cite: 1992, 1993]
                    if bboxes_len > 1:  # [cite: 1994, 1995]
                        has_multi_face = True  # [cite: 1995, 1996]
                    bbox = bboxes[0]  # [cite: 1996, 1997, 1998]
                    x1_cell, y1_cell, x2, y2, score = bbox.astype(int)  # [cite: 1998, 1999, 2000, 2001, 2002]
                    # Bbox adjustment logic
                    x1 = max(x1_cell - int((x2 - x1_cell) * 0.1), 0)
                    x2 = x2 + int((x2 - x1_cell) * 0.1)
                    y2_adjusted = y2 + int((y2 - y1_cell) * 0.1)  # Renamed to avoid conflict
                    y1 = max(y1_cell, 0)
                    face_img = frame[y1:y2_adjusted, x1:x2]  # [cite: 2019, 2020, 2021, 2022]
                    pots = plfd.forward(face_img)[0]  # [cite: 2023, 2024, 2025]
                    # This list comprehension uses x1, y1 as free vars if they were from cell
                    # Assuming x1_cell, y1_cell are the correct ones from the bbox
                    landmarks = np.array([[x1_cell + x, y1_cell + y] for x, y in pots.astype(np.int32)])  #
                    _xmin, _ymin, w, h = cv2.boundingRect(
                        np.array(landmarks))  # [cite: 2033, 2034, 2035, 2036, 2037, 2038]
                    wh_list.append(w / h)  # [cite: 2038, 2039, 2040, 2041]
                count += 1  # [cite: 2041, 2042, 2043]
        except Exception as e1_loop:  # [cite: 2044, 2045]
            logger.error(f"[{code}]init_wh exception: {e1_loop}")  # [cite: 2048, 2049, 2050]
        finally:  # [cite: 2055]
            cap.release()  # [cite: 2055, 2056, 2057]
        wh_val = 0  # [cite: 2060, 2061]
        if len(wh_list) > 0:  # [cite: 2058, 2059] (Implicit, original compares to 0)
            wh_val = np.mean(np.array(wh_list))  # [cite: 2061, 2062, 2063, 2064, 2065]
        logger.info(
            f"[{code}]init_wh result :[{wh_val}]， cost: {time.time() - s} s")  # [cite: 2065, 2066, 2067, 2068, 2069, 2070]
        torch.cuda.empty_cache()  # [cite: 2070, 2071, 2072]
        out_queue_suan.append([code, wh_val, has_multi_face])  # [cite: 2072, 2073, 2074, 2075]
        print(out_queue_suan)
        return out_queue_suan
    except Exception as e_main:  # [cite: 2077, 2078]
        print(traceback.format_exc())  # [cite: 2080, 2081, 2082]
        print('失败')
        torch.cuda.empty_cache()  # [cite: 2089, 2090]


def get_video_info(video_file):
    cap = cv2.VideoCapture(video_file)
    fps = round(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
    cap.release()
    return (
     fps, width, height, fourcc)


def get_video_info_suan(video_file):
    cap = cv2.VideoCapture(video_file)
    fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
    cap.release()
    return fourcc


def format_audio(audio_path):
    code = str(int(time.time()))
    ffmpeg_command = "ffmpeg -loglevel warning -i %s -ac 1 -ar 16000 -acodec pcm_s16le -y  %s"
    audio_format = os.path.join('data/temp_audio', code + "_format.wav")
    ffmpeg_command = ffmpeg_command % (audio_path, audio_format)
    os.system(ffmpeg_command)
    if not os.path.exists(audio_format):
        raise Exception("format audio error")
    return audio_format

class TransDhTask(object):
    def __init__(self, *args, **kwargs):
        logger.info("TransDhTask init")
        set_start_method("spawn", force=True)
        self.run_lock = threading.Lock()
        self.task_dic = {}
        self.run_flag = False
        self.batch_size = int(GlobalConfig.instance().batch_size)
        self.drivered_queue = multiprocessing.Queue(5000)
        self.output_imgs_queue = multiprocessing.Queue(5000)
        self.result_queue = multiprocessing.Queue(1)
        self.out_queue_suan = []
        self.frame_list = []
        self.audio_list = []
        self.op = []
        self.temp = 'example/video.mp4'
        self.fps, self.width, self.height, self.fourcc = get_video_info(self.temp)
        self.wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")
        self.digital_human_model = DigitalHumanModel(
            GlobalConfig.instance().blend_dynamic,
            GlobalConfig.instance().chaofen_before,
            False
        )
        self.out_queue_suan = init_wh_process_suan(self.temp,self.out_queue_suan)
        self.frame_list = drivered_video_pn_suan(self.temp,self.frame_list)
        wh_output = self.out_queue_suan[0]
        wh = wh_output[1]
        self.digital_human_model.drivered_wh = wh
        self.op = self.save_op( self.frame_list,wh)
        

    def train_suan(self,temp_mp4_file):
        self.out_queue_suan = []
        self.frame_list = []
        self.op = []
        self.fps, self.width, self.height, self.fourcc = get_video_info(temp_mp4_file)
        self.out_queue_suan = init_wh_process_suan(temp_mp4_file,self.out_queue_suan)
        self.frame_list = drivered_video_pn_suan(temp_mp4_file,self.frame_list)
        wh_output = self.out_queue_suan[0]
        wh = wh_output[1]
        self.digital_human_model.drivered_wh = wh
        self.op = self.save_op( self.frame_list,wh)

    @classmethod
    def instance(cls, *args, **kwargs):
        if not hasattr(TransDhTask, "_instance"):
            TransDhTask._instance = TransDhTask(*args, **kwargs)
        return TransDhTask._instance

    def auidp_xs(self,audio_wenet_feature,wenet_feature_list):
        for i in range(len(audio_wenet_feature)):
            wenet_feature_list.append([audio_wenet_feature[i]])
        return wenet_feature_list

    def save_op(self,ll_list,wh):
        def warp_imgs(imgs_data):
            caped_img2 = {idx: {'imgs_data':it,  'idx':idx} for it, idx in zip(imgs_data, range(len(imgs_data)))}
            return caped_img2
        scrfd_predictor = pfpld(False, 'face_detect_utils/resources/')
        hp = Headpose(False, 'face_detect_utils/resources/model_float32.onnx')
        scrfd_detector = FaceDetect(model_path='face_detect_utils/resources/')
        jh = []
        for img_list in ll_list:
            # 读取图片数据
            caped_drivered_img2 = warp_imgs(img_list)
            drivered_op = op(
                caped_drivered_img2, wh, scrfd_detector, scrfd_predictor, hp, None,
                256, False
            )
            drivered_op.flow()
            drivered_face_dict = drivered_op.mp_dict
            jh.append(drivered_face_dict)
        return jh

    def load_wav(self,wav_f, sr=None):
        wav_arr, _ = librosa.load(wav_f, sr=sr)
        return wav_arr
    def video_cl(self,video_path, fourcc):
        code = str(int(time.time()))
        if fourcc == cv2.VideoWriter_fourcc("H", "2", "6", "4") or fourcc == cv2.VideoWriter_fourcc("a", "v", "c", "1") or fourcc == cv2.VideoWriter_fourcc("h", "2", "6", "4"):
            ffmpeg_command = "ffmpeg -loglevel warning -i %s -crf 15 -vcodec copy -an -y %s"
        else:
            ffmpeg_command = "ffmpeg -loglevel warning -i %s -c:v libx264 -crf 15 -an -y %s"
        video_format = os.path.join('data_info_av', code + "_format.mp4")
        ffmpeg_command = ffmpeg_command % (video_path, video_format)
        print("[%s] -> ffmpeg video: %s", code, ffmpeg_command)
        os.system(ffmpeg_command)
        if not os.path.exists(video_format):
            raise Exception("format video error")
        return video_format
    def work_ausn(self, mp4_Path,audio_url):
        fourcc = get_video_info_suan(mp4_Path)
        mp4_Path = self.video_cl(mp4_Path,fourcc)
        self.train_suan(mp4_Path)
        self.audio_list = []
        _audio_data = self.load_wav(audio_url,16000)
        self.audio_list = feature_extraction_wenet(_audio_data, fps=self.fps, wenet_model=(self.wenet_model))
        for batch in range(1):
            print(f"开始处理批次 {batch + 1}")
            results = audio_transfer_suan(
                self.digital_human_model, self.frame_list, self.audio_list, self.op,self.width,self.height,batch_size=1
            )
            print(f"批次 {batch + 1} 处理完成")
            print('=================================================================================================')
        dd = write_video_suan(results,self.width,self.height,self.fps,audio_url)
        return dd